using System;
using System.Collections.Generic;
using System.IO;
using System.Runtime.InteropServices;
using System.Threading.Tasks;
using System.Windows.Forms;
using System.Media;
using System.Threading;

namespace IPIS.Utils
{
    public class AudioPlayer : IDisposable
    {
        private SoundPlayer soundPlayer;
        private bool isDisposed = false;
        private int currentVolume = 50;
        private bool isPlaying = false;
        private bool isPaused = false;
        private List<string> audioQueue;
        private int currentAudioIndex = 0;
        private int repeatCount = 1;
        private int currentRepeat = 0;
        private System.Windows.Forms.Timer audioTimer;
        private System.Windows.Forms.Timer playbackCheckTimer;
        private Action<string> statusCallback;
        private DateTime audioStartTime;
        private TimeSpan audioDuration;

        public event EventHandler PlaybackCompleted;
        public event EventHandler<string> StatusChanged;

        public AudioPlayer()
        {
            InitializeAudioPlayer();
            audioQueue = new List<string>();
            InitializeTimers();
        }

        private void InitializeAudioPlayer()
        {
            try
            {
                soundPlayer = new SoundPlayer();
                LogDebug("Audio player initialized successfully");
            }
            catch (Exception ex)
            {
                LogDebug($"Error initializing audio player: {ex.Message}");
                throw;
            }
        }

        private void InitializeTimers()
        {
            audioTimer = new System.Windows.Forms.Timer();
            audioTimer.Interval = AudioConfig.AUDIO_TIMER_INTERVAL_MS;
            audioTimer.Tick += AudioTimer_Tick;

            playbackCheckTimer = new System.Windows.Forms.Timer();
            playbackCheckTimer.Interval = AudioConfig.PLAYBACK_CHECK_INTERVAL_MS;
            playbackCheckTimer.Tick += PlaybackCheckTimer_Tick;
        }

        private void AudioTimer_Tick(object sender, EventArgs e)
        {
            // AudioTimer is only used to start initial playback
            // PlaybackCheckTimer handles moving to next audio files
            if (soundPlayer != null && !isPlaying && !isPaused && audioQueue.Count > 0 && currentAudioIndex == 0)
            {
                isPlaying = true;
                PlayNextAudio();
                audioTimer.Stop(); // Stop after starting initial playback
            }
        }

        private void PlaybackCheckTimer_Tick(object sender, EventArgs e)
        {
            if (isPlaying && !isPaused && audioStartTime != DateTime.MinValue)
            {
                // Check if current audio has finished playing
                TimeSpan elapsed = DateTime.Now - audioStartTime;

                // Add a buffer to ensure audio has actually finished
                TimeSpan bufferTime = TimeSpan.FromMilliseconds(AudioConfig.AUDIO_COMPLETION_BUFFER_MS);
                if (elapsed >= (audioDuration + bufferTime))
                {
                    // Current audio finished, move to next
                    playbackCheckTimer.Stop();
                    LogDebug($"Audio file completed after {elapsed.TotalSeconds:F2}s (duration: {audioDuration.TotalSeconds:F2}s), moving to next");
                    MoveToNextAudio();
                }
            }
        }

        public void SetVolume(int volume)
        {
            if (volume < AudioConfig.MIN_VOLUME) volume = AudioConfig.MIN_VOLUME;
            if (volume > AudioConfig.MAX_VOLUME) volume = AudioConfig.MAX_VOLUME;

            currentVolume = volume;
            // Use the VolumeControl utility to actually control system volume
            VolumeControl.SetSystemVolume(volume);
            LogDebug($"Volume set to {volume}% using system volume control");
        }

        public int GetVolume()
        {
            return currentVolume;
        }

        public void SetAudioQueue(List<string> audioFiles, int repeats = AudioConfig.DEFAULT_REPEAT_COUNT)
        {
            audioQueue.Clear();
            audioQueue.AddRange(audioFiles);
            repeatCount = repeats;
            currentAudioIndex = 0;
            currentRepeat = 0;
            LogDebug($"Audio queue set with {audioFiles.Count} files, {repeats} repeat(s)");
        }

        public void StartPlayback()
        {
            if (audioQueue.Count == 0)
            {
                UpdateStatus("No audio files in queue");
                return;
            }

            isPlaying = false; // Let AudioTimer handle setting this to true
            isPaused = false;
            currentAudioIndex = 0;
            currentRepeat = 0;
            audioTimer.Start(); // AudioTimer will start the first audio and set isPlaying = true
            UpdateStatus($"Playback started with {repeatCount} repeat(s)");
        }

        public void PausePlayback()
        {
            if (isPlaying && soundPlayer != null)
            {
                soundPlayer.Stop();
                isPlaying = false;
                isPaused = true;
                playbackCheckTimer.Stop();
                UpdateStatus("Playback paused");
            }
        }

        public void ResumePlayback()
        {
            if (isPaused)
            {
                isPlaying = true;
                isPaused = false;
                PlayNextAudio();
                UpdateStatus("Playback resumed");
            }
        }

        public void StopPlayback()
        {
            if (soundPlayer != null)
            {
                soundPlayer.Stop();
            }
            isPlaying = false;
            isPaused = false;
            audioTimer.Stop();
            playbackCheckTimer.Stop();
            audioQueue.Clear();
            currentAudioIndex = 0;
            currentRepeat = 0;
            audioStartTime = DateTime.MinValue;
            audioDuration = TimeSpan.Zero;
            UpdateStatus("Playback stopped");
        }

        private void PlayNextAudio()
        {
            try
            {
                if (audioQueue == null || audioQueue.Count == 0 || currentAudioIndex >= audioQueue.Count)
                {
                    LogDebug("No more audio files to play");
                    return;
                }

                string audioFile = audioQueue[currentAudioIndex];
                if (string.IsNullOrEmpty(audioFile) || !File.Exists(audioFile))
                {
                    LogDebug($"Audio file not found or empty: {audioFile}");
                    currentAudioIndex++;
                    PlayNextAudio();
                    return;
                }

                LogDebug($"Playing audio file {currentAudioIndex + 1}/{audioQueue.Count}: {Path.GetFileName(audioFile)}");
                UpdateStatus($"Playing: {Path.GetFileName(audioFile)}");

                // Ensure isPlaying is set to true
                isPlaying = true;

                // Get audio duration using WaveFileDuration utility
                try
                {
                    double durationSeconds = WaveFileDuration.GetDuration(audioFile);
                    audioDuration = TimeSpan.FromSeconds(durationSeconds);
                    LogDebug($"Audio duration: {audioDuration.TotalSeconds:F2} seconds");
                }
                catch (Exception ex)
                {
                    LogDebug($"Error getting audio duration: {ex.Message}, using default {AudioConfig.DEFAULT_AUDIO_DURATION_SECONDS} seconds");
                    audioDuration = TimeSpan.FromSeconds(AudioConfig.DEFAULT_AUDIO_DURATION_SECONDS);
                }

                // Play audio asynchronously
                soundPlayer.SoundLocation = audioFile;
                soundPlayer.Play(); // Use Play() instead of PlaySync() for non-blocking playback

                // Start timing the playback
                audioStartTime = DateTime.Now;
                playbackCheckTimer.Start();
                LogDebug($"Started playback timer for {Path.GetFileName(audioFile)} - will check completion after {audioDuration.TotalSeconds:F2}s");
            }
            catch (Exception ex)
            {
                LogDebug($"Error in PlayNextAudio: {ex.Message}");
                currentAudioIndex++;
                PlayNextAudio();
            }
        }

        private void MoveToNextAudio()
        {
            // Move to next audio file
            currentAudioIndex++;
            if (currentAudioIndex >= audioQueue.Count)
            {
                if (currentRepeat < repeatCount - 1)
                {
                    currentRepeat++;
                    currentAudioIndex = 0;
                    LogDebug($"Starting repeat {currentRepeat + 1} of {repeatCount}");
                    // Start the next repeat immediately
                    PlayNextAudio();
                }
                else
                {
                    LogDebug("Playback completed");
                    isPlaying = false;
                    audioTimer.Stop();
                    PlaybackCompleted?.Invoke(this, EventArgs.Empty);
                    UpdateStatus("Playback completed");
                }
            }
            else if (isPlaying && !isPaused)
            {
                // Handle delay between audio files
                if (AudioConfig.DELAY_BETWEEN_AUDIO_FILES_MS > 0)
                {
                    // Add a configurable delay between audio files for better flow
                    // Use a timer to ensure we're on the UI thread
                    var delayTimer = new System.Windows.Forms.Timer();
                    delayTimer.Interval = AudioConfig.DELAY_BETWEEN_AUDIO_FILES_MS;
                    delayTimer.Tick += (s, e) =>
                    {
                        delayTimer.Stop();
                        delayTimer.Dispose();
                        if (isPlaying && !isPaused)
                        {
                            PlayNextAudio();
                        }
                    };
                    delayTimer.Start();
                }
                else
                {
                    // No delay - play next audio immediately
                    PlayNextAudio();
                }
            }
        }

        public bool IsPlaying => isPlaying;
        public bool IsPaused => isPaused;
        public int CurrentAudioIndex => currentAudioIndex;
        public int TotalAudioFiles => audioQueue.Count;

        public void SetStatusCallback(Action<string> callback)
        {
            statusCallback = callback;
        }

        private void UpdateStatus(string message)
        {
            StatusChanged?.Invoke(this, message);
            statusCallback?.Invoke(message);
        }

        private void LogDebug(string message)
        {
            if (AudioConfig.ENABLE_AUDIO_DEBUG_LOGGING)
            {
                string timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss.fff");
                string logMessage = $"[{timestamp}] AudioPlayer: {message}";
                Console.WriteLine(logMessage);
            }
        }

        public void Dispose()
        {
            if (!isDisposed)
            {
                try
                {
                    if (audioTimer != null)
                    {
                        audioTimer.Stop();
                        audioTimer.Dispose();
                        audioTimer = null;
                    }

                    if (playbackCheckTimer != null)
                    {
                        playbackCheckTimer.Stop();
                        playbackCheckTimer.Dispose();
                        playbackCheckTimer = null;
                    }

                    if (soundPlayer != null)
                    {
                        soundPlayer.Stop();
                        soundPlayer.Dispose();
                        soundPlayer = null;
                    }

                    if (audioQueue != null)
                    {
                        audioQueue.Clear();
                        audioQueue = null;
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error disposing AudioPlayer: {ex.Message}");
                }
                finally
                {
                    isDisposed = true;
                }
            }
        }
    }


}