using System;

namespace IPIS.Utils
{
    /// <summary>
    /// Global audio configuration settings
    /// Centralized configuration for audio playback behavior
    /// </summary>
    public static class AudioConfig
    {
        /// <summary>
        /// Delay between audio files in milliseconds
        /// Default: 0ms (no delay) - For fastest transitions between audio files
        /// Set to higher values if audio files need breathing room
        /// </summary>
        public const int DELAY_BETWEEN_AUDIO_FILES_MS = 10;

        /// <summary>
        /// Default volume level (0-100)
        /// Default: 50 (50%)
        /// </summary>
        public const int DEFAULT_VOLUME = 50;

        /// <summary>
        /// Default repeat count for audio playback
        /// Default: 1 (no repeat)
        /// </summary>
        public const int DEFAULT_REPEAT_COUNT = 1;

        /// <summary>
        /// Audio playback check interval in milliseconds
        /// How often to check if current audio has finished
        /// Default: 10ms (higher responsiveness for faster transitions)
        /// </summary>
        public const int PLAYBACK_CHECK_INTERVAL_MS = 10;

        /// <summary>
        /// Audio timer interval in milliseconds
        /// Used for initial playback start
        /// Default: 100ms
        /// </summary>
        public const int AUDIO_TIMER_INTERVAL_MS = 100;

        /// <summary>
        /// Buffer time in milliseconds to ensure audio has actually finished
        /// Added to audio duration to prevent premature next file
        /// Default: 50ms - Reduced for faster transitions
        /// </summary>
        public const int AUDIO_COMPLETION_BUFFER_MS = 50;

        /// <summary>
        /// Default audio duration in seconds when actual duration cannot be determined
        /// Default: 3 seconds
        /// </summary>
        public const double DEFAULT_AUDIO_DURATION_SECONDS = 3.0;

        /// <summary>
        /// Whether to enable debug logging for audio operations
        /// Default: true
        /// </summary>
        public const bool ENABLE_AUDIO_DEBUG_LOGGING = false;

        /// <summary>
        /// Maximum volume level allowed
        /// </summary>
        public const int MAX_VOLUME = 100;

        /// <summary>
        /// Minimum volume level allowed
        /// </summary>
        public const int MIN_VOLUME = 0;

        /// <summary>
        /// Maximum repeat count allowed
        /// </summary>
        public const int MAX_REPEAT_COUNT = 10;

        /// <summary>
        /// Minimum repeat count allowed
        /// </summary>
        public const int MIN_REPEAT_COUNT = 1;
    }
} 