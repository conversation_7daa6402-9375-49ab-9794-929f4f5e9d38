using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;
using IPIS.Models;
using IPIS.Repositories;
using IPIS.Services;
using IPIS.Forms.Settings;
using IPIS.Forms.Settings.Announcement;
using IPIS.Forms.Configuration;
using IPIS.Utils;

namespace IPIS.Forms.Settings
{
    public partial class SystemSettingsForm : Form
    {
        private readonly LanguageService _languageService;
        private readonly LoginConfigurationService _loginConfigService;
        private LoginConfiguration _currentLoginConfig;

        // Login Configuration Controls
        private GroupBox stationInfoGroup;
        private GroupBox appearanceGroup;
        private GroupBox imagesGroup;
        private GroupBox previewGroup;

        private Label lblStationName;
        private TextBox txtStationName;
        private Label lblWelcomeMessage;
        private TextBox txtWelcomeMessage;
        private Label lblSubtitle;
        private TextBox txtSubtitle;

        private Label lblPrimaryColor;
        private Button btnPrimaryColor;
        private Label lblStationTextColor;
        private Button btnStationTextColor;

        private CheckBox chkUseCustomLogo;
        private Label lblLogoPath;
        private TextBox txtLogoPath;
        private Button btnBrowseLogo;
        private CheckBox chkUseBackgroundImage;
        private Label lblBackgroundImagePath;
        private TextBox txtBackgroundImagePath;
        private Button btnBrowseBackground;

        private Panel previewPanel;
        private Button btnSaveLoginConfig;
        private Button btnResetLoginConfig;

        private ColorDialog colorDialog;
        private OpenFileDialog openFileDialog;

        public SystemSettingsForm()
        {
            _languageService = new LanguageService(new SQLiteLanguageRepository());
            _loginConfigService = new LoginConfigurationService(new SQLiteLoginConfigurationRepository());
            _currentLoginConfig = _loginConfigService.GetLoginConfiguration();

            InitializeComponent();
            InitializeLoginConfigDialogs();

            // Restrict displayTab, databaseTab, networkTab, loggingTab to Super Admin (Administrator) only
            if (!SessionManager.IsAdministrator())
            {
                if (this.tabControl.TabPages.Contains(this.displayTab))
                {
                    this.tabControl.TabPages.Remove(this.displayTab);
                }
                if (this.tabControl.TabPages.Contains(this.databaseTab))
                {
                    this.tabControl.TabPages.Remove(this.databaseTab);
                }
                if (this.tabControl.TabPages.Contains(this.networkTab))
                {
                    this.tabControl.TabPages.Remove(this.networkTab);
                }
                if (this.tabControl.TabPages.Contains(this.loggingTab))
                {
                    this.tabControl.TabPages.Remove(this.loggingTab);
                }
            }
            // Restrict languageTab to Supervisor and Operator only
            if (!(SessionManager.IsSupervisor() || SessionManager.IsOperator() || SessionManager.IsAdministrator()))
            {
                // Hide or disable the language tab for other roles
                if (this.tabControl.TabPages.Contains(this.languageTab))
                {
                    this.tabControl.TabPages.Remove(this.languageTab);
                }
            }
            // Restrict loginConfigTab to Supervisor and Operator only
            if (!(SessionManager.IsSupervisor() || SessionManager.IsOperator() || SessionManager.IsAdministrator()))
            {
                if (this.tabControl.TabPages.Contains(this.loginConfigTab))
                {
                    this.tabControl.TabPages.Remove(this.loginConfigTab);
                }
            }
            // announcementTab is allowed for all, so no restriction needed
        }

        private void InitializeComponent()
        {
            this.tabControl = new TabControl();
            this.displayTab = new TabPage();
            this.databaseTab = new TabPage();
            this.networkTab = new TabPage();
            this.loggingTab = new TabPage();
            this.languageTab = new TabPage();
            this.announcementTab = new TabPage();
            this.loginConfigTab = new TabPage();
            this.statusStrip = new StatusStrip();
            this.statusLabel = new ToolStripStatusLabel();

            // SystemSettingsForm
            this.ClientSize = new System.Drawing.Size(800, 600);
            this.Name = "SystemSettingsForm";
            this.Text = "System Settings";
            this.WindowState = FormWindowState.Maximized;

            // TabControl
            this.tabControl.Dock = DockStyle.Fill;
            this.tabControl.Name = "tabControl";

            // Display Tab
            this.displayTab.Text = "Display Settings";
            this.displayTab.UseVisualStyleBackColor = true;
            this.InitializeDisplayTab();

            // Database Tab
            this.databaseTab.Text = "Database Settings";
            this.databaseTab.UseVisualStyleBackColor = true;
            this.InitializeDatabaseTab();

            // Network Tab
            this.networkTab.Text = "Network Settings";
            this.networkTab.UseVisualStyleBackColor = true;
            this.InitializeNetworkTab();

            // Logging Tab
            this.loggingTab.Text = "Logging Settings";
            this.loggingTab.UseVisualStyleBackColor = true;
            this.InitializeLoggingTab();

            // Language Tab
            this.languageTab.Text = "Language Settings";
            this.languageTab.UseVisualStyleBackColor = true;

            // Announcement Tab
            this.announcementTab.Text = "Announcement Settings";
            this.announcementTab.UseVisualStyleBackColor = true;

            // Login Configuration Tab
            this.loginConfigTab.Text = "Login Configuration";
            this.loginConfigTab.UseVisualStyleBackColor = true;

            // Add tabs to TabControl
            this.tabControl.Controls.AddRange(new Control[] {
                this.displayTab,
                this.databaseTab,
                this.networkTab,
                this.loggingTab,
                this.languageTab,
                this.announcementTab,
                this.loginConfigTab
            });

            // StatusStrip
            this.statusStrip.Items.AddRange(new ToolStripItem[] {
                this.statusLabel
            });
            this.statusStrip.Location = new System.Drawing.Point(0, 578);
            this.statusStrip.Name = "statusStrip";
            this.statusStrip.Size = new System.Drawing.Size(800, 22);
            this.statusStrip.TabIndex = 1;

            // Status Label
            this.statusLabel.Name = "statusLabel";
            this.statusLabel.Text = "Ready";

            // Add controls to form
            this.Controls.AddRange(new Control[] {
                this.tabControl,
                this.statusStrip
            });

            // Add language management control to language tab
            var languageManagementControl = new LanguageManagementControl();
            this.languageTab.Controls.Add(languageManagementControl);

            // Add announcement management control to announcement tab
            var announcementManagementControl = new AnnouncementManagementControl();
            this.announcementTab.Controls.Add(announcementManagementControl);

            // Add login configuration control to login config tab
            InitializeLoginConfigTab();
        }

        private TabControl tabControl;
        private TabPage displayTab;
        private TabPage databaseTab;
        private TabPage networkTab;
        private TabPage loggingTab;
        private TabPage languageTab;
        private TabPage announcementTab;
        private TabPage loginConfigTab;
        private StatusStrip statusStrip;
        private ToolStripStatusLabel statusLabel;

        private void InitializeDisplayTab()
        {
            // Display settings controls will be added here
            Label displaySettingsLabel = new Label
            {
                AutoSize = true,
                Location = new System.Drawing.Point(20, 20),
                Text = "Display settings will be implemented later."
            };
            this.displayTab.Controls.Add(displaySettingsLabel);
        }

        private void InitializeDatabaseTab()
        {
            // Database settings controls will be added here
            Label databaseSettingsLabel = new Label
            {
                AutoSize = true,
                Location = new System.Drawing.Point(20, 20),
                Text = "Database settings will be implemented later."
            };
            this.databaseTab.Controls.Add(databaseSettingsLabel);
        }

        private void InitializeNetworkTab()
        {
            // Network settings controls will be added here
            Label networkSettingsLabel = new Label
            {
                AutoSize = true,
                Location = new System.Drawing.Point(20, 20),
                Text = "Network settings will be implemented later."
            };
            this.networkTab.Controls.Add(networkSettingsLabel);
        }

        private void InitializeLoggingTab()
        {
            // Logging settings controls will be added here
            Label loggingSettingsLabel = new Label
            {
                AutoSize = true,
                Location = new System.Drawing.Point(20, 20),
                Text = "Logging settings will be implemented later."
            };
            this.loggingTab.Controls.Add(loggingSettingsLabel);
        }

        private void InitializeLoginConfigTab()
        {
            // Create main panel that fills the entire tab
            Panel mainPanel = new Panel
            {
                Dock = DockStyle.Fill,
                Padding = new Padding(10),
                BackColor = Color.FromArgb(240, 244, 248),
                AutoScroll = true
            };

            CreateLoginConfigControls();
            LayoutLoginConfigControlsFullScreen(mainPanel);
            SetupLoginConfigEventHandlers();
            LoadLoginConfiguration();

            this.loginConfigTab.Controls.Add(mainPanel);
        }

        private void CreateLoginConfigControls()
        {
            // Group boxes
            stationInfoGroup = new GroupBox
            {
                Text = "Station Information",
                Font = new Font("Segoe UI", 10, FontStyle.Bold),
                ForeColor = Color.FromArgb(33, 37, 41)
            };

            appearanceGroup = new GroupBox
            {
                Text = "Colors & Appearance",
                Font = new Font("Segoe UI", 10, FontStyle.Bold),
                ForeColor = Color.FromArgb(33, 37, 41)
            };

            imagesGroup = new GroupBox
            {
                Text = "Images & Branding",
                Font = new Font("Segoe UI", 10, FontStyle.Bold),
                ForeColor = Color.FromArgb(33, 37, 41)
            };

            // previewGroup = new GroupBox
            // {
            //     Text = "Preview",
            //     Font = new Font("Segoe UI", 10, FontStyle.Bold),
            //     ForeColor = Color.FromArgb(33, 37, 41)
            // };

            // Station Information controls
            lblStationName = new Label { Text = "Station Name:", AutoSize = true };
            txtStationName = new TextBox { Size = new Size(200, 25) };

            lblWelcomeMessage = new Label { Text = "Welcome Message:", AutoSize = true };
            txtWelcomeMessage = new TextBox { Size = new Size(200, 25) };

            lblSubtitle = new Label { Text = "Subtitle:", AutoSize = true };
            txtSubtitle = new TextBox { Size = new Size(200, 50), Multiline = true };

            // Color controls
            lblPrimaryColor = new Label
            {
                Text = "Background Color:",
                AutoSize = true,
                Font = new Font("Segoe UI", 9, FontStyle.Regular)
            };
            btnPrimaryColor = new Button
            {
                Size = new Size(80, 30),
                FlatStyle = FlatStyle.Flat,
                Text = "",
                Cursor = Cursors.Hand
            };
            btnPrimaryColor.FlatAppearance.BorderSize = 1;
            btnPrimaryColor.FlatAppearance.BorderColor = Color.FromArgb(206, 212, 218);

            lblStationTextColor = new Label
            {
                Text = "Text Color:",
                AutoSize = true,
                Font = new Font("Segoe UI", 9, FontStyle.Regular)
            };
            btnStationTextColor = new Button
            {
                Size = new Size(80, 30),
                FlatStyle = FlatStyle.Flat,
                Text = "",
                Cursor = Cursors.Hand
            };
            btnStationTextColor.FlatAppearance.BorderSize = 1;
            btnStationTextColor.FlatAppearance.BorderColor = Color.FromArgb(206, 212, 218);

            // Image controls
            chkUseCustomLogo = new CheckBox { Text = "Use Custom Logo", AutoSize = true };
            lblLogoPath = new Label { Text = "Logo Path:", AutoSize = true };
            txtLogoPath = new TextBox { Size = new Size(250, 25), ReadOnly = true };
            btnBrowseLogo = new Button { Text = "Browse...", Size = new Size(75, 25) };

            chkUseBackgroundImage = new CheckBox { Text = "Use Background Image", AutoSize = true };
            lblBackgroundImagePath = new Label { Text = "Background Image:", AutoSize = true };
            txtBackgroundImagePath = new TextBox { Size = new Size(250, 25), ReadOnly = true };
            btnBrowseBackground = new Button { Text = "Browse...", Size = new Size(75, 25) };

            // Preview panel
            previewPanel = new Panel
            {
                Size = new Size(300, 200),
                BorderStyle = BorderStyle.FixedSingle,
                BackColor = Color.White
            };

            // Action buttons
            btnSaveLoginConfig = new Button
            {
                Text = "Save Configuration",
                Size = new Size(130, 35),
                BackColor = Color.FromArgb(40, 167, 69),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            btnSaveLoginConfig.FlatAppearance.BorderSize = 0;

            btnResetLoginConfig = new Button
            {
                Text = "Reset",
                Size = new Size(120, 35),
                BackColor = Color.FromArgb(220, 53, 69),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            btnResetLoginConfig.FlatAppearance.BorderSize = 0;


        }

        private void LayoutLoginConfigControlsFullScreen(Panel mainPanel)
        {
            // Calculate available space
            int availableWidth = mainPanel.Width - 40; // Account for padding
            int availableHeight = mainPanel.Height - 40;

            // Use TableLayoutPanel for better responsive layout
            TableLayoutPanel tableLayout = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 3,
                RowCount = 3,
                Padding = new Padding(10)
            };

            // Set column styles for responsive layout
            tableLayout.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 35F)); // Station Info
            tableLayout.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 35F)); // Appearance & Images
            tableLayout.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 30F)); // Preview

            // Set row styles
            tableLayout.RowStyles.Add(new RowStyle(SizeType.Percent, 40F)); // Top row
            tableLayout.RowStyles.Add(new RowStyle(SizeType.Percent, 50F)); // Middle row
            tableLayout.RowStyles.Add(new RowStyle(SizeType.Percent, 10F)); // Bottom row (buttons)

            mainPanel.Controls.Add(tableLayout);

            // Station Info Group - spans full height of first two rows
            stationInfoGroup.Dock = DockStyle.Fill;
            stationInfoGroup.Controls.AddRange(new Control[] {
                lblStationName, txtStationName, lblWelcomeMessage, txtWelcomeMessage, lblSubtitle, txtSubtitle
            });
            tableLayout.Controls.Add(stationInfoGroup, 0, 0);
            tableLayout.SetRowSpan(stationInfoGroup, 2);

            // Layout station info controls
            lblStationName.Location = new Point(15, 30);
            txtStationName.Location = new Point(15, 50);
            txtStationName.Size = new Size(250, 25);

            lblWelcomeMessage.Location = new Point(15, 85);
            txtWelcomeMessage.Location = new Point(15, 105);
            txtWelcomeMessage.Size = new Size(250, 25);

            lblSubtitle.Location = new Point(15, 140);
            txtSubtitle.Location = new Point(15, 160);
            txtSubtitle.Size = new Size(250, 80);

            // Appearance Group - top right
            appearanceGroup.Dock = DockStyle.Fill;
            appearanceGroup.Controls.AddRange(new Control[] {
                lblPrimaryColor, btnPrimaryColor, lblStationTextColor, btnStationTextColor
            });
            tableLayout.Controls.Add(appearanceGroup, 1, 0);

            lblPrimaryColor.Location = new Point(15, 30);
            btnPrimaryColor.Location = new Point(15, 50);
            btnPrimaryColor.Size = new Size(100, 35);

            lblStationTextColor.Location = new Point(15, 95);
            btnStationTextColor.Location = new Point(15, 115);
            btnStationTextColor.Size = new Size(100, 35);

            // Images Group - middle right
            imagesGroup.Dock = DockStyle.Fill;
            imagesGroup.Controls.AddRange(new Control[] {
                chkUseCustomLogo, lblLogoPath, txtLogoPath, btnBrowseLogo,
                chkUseBackgroundImage, lblBackgroundImagePath, txtBackgroundImagePath, btnBrowseBackground
            });
            tableLayout.Controls.Add(imagesGroup, 1, 1);

            chkUseCustomLogo.Location = new Point(15, 25);
            lblLogoPath.Location = new Point(15, 50);
            txtLogoPath.Location = new Point(15, 70);
            txtLogoPath.Size = new Size(200, 25);
            btnBrowseLogo.Location = new Point(220, 70);

            chkUseBackgroundImage.Location = new Point(15, 105);
            lblBackgroundImagePath.Location = new Point(15, 130);
            txtBackgroundImagePath.Location = new Point(15, 150);
            txtBackgroundImagePath.Size = new Size(200, 25);
            btnBrowseBackground.Location = new Point(220, 150);

            // Preview Group - spans full height on the right
            // previewGroup.Dock = DockStyle.Fill;
            // previewGroup.Controls.Add(previewPanel);
            // tableLayout.Controls.Add(previewGroup, 2, 0);
            // tableLayout.SetRowSpan(previewGroup, 2);

            previewPanel.Dock = DockStyle.Fill;
            previewPanel.Margin = new Padding(10);

            // Action buttons panel - spans all columns at bottom
            Panel buttonPanel = new Panel
            {
                Dock = DockStyle.Fill,
                Height = 50
            };

            buttonPanel.Controls.AddRange(new Control[] { btnSaveLoginConfig, btnResetLoginConfig });
            tableLayout.Controls.Add(buttonPanel, 0, 2);
            tableLayout.SetColumnSpan(buttonPanel, 3);

            btnSaveLoginConfig.Location = new Point(20, 10);
            btnResetLoginConfig.Location = new Point(170, 10);
        }

        private void InitializeLoginConfigDialogs()
        {
            colorDialog = new ColorDialog();
            openFileDialog = new OpenFileDialog
            {
                Filter = "Image Files|*.jpg;*.jpeg;*.png;*.bmp;*.gif;*.ico",
                Title = "Select Image File"
            };
        }

        private void SetupLoginConfigEventHandlers()
        {
            // Color button events
            btnPrimaryColor.Click += (s, e) => SelectLoginColor(btnPrimaryColor, "Primary Color");
            btnStationTextColor.Click += (s, e) => SelectLoginColor(btnStationTextColor, "Station Text Color");

            // File browser events
            btnBrowseLogo.Click += BtnBrowseLogo_Click;
            btnBrowseBackground.Click += BtnBrowseBackground_Click;

            // Checkbox events
            chkUseCustomLogo.CheckedChanged += ChkUseCustomLogo_CheckedChanged;
            chkUseBackgroundImage.CheckedChanged += ChkUseBackgroundImage_CheckedChanged;

            // Action button events
            btnSaveLoginConfig.Click += BtnSaveLoginConfig_Click;
            btnResetLoginConfig.Click += BtnResetLoginConfig_Click;

            // Text change events for live preview
            txtStationName.TextChanged += (s, e) => UpdateLoginPreview();
            txtWelcomeMessage.TextChanged += (s, e) => UpdateLoginPreview();
            txtSubtitle.TextChanged += (s, e) => UpdateLoginPreview();
        }

        private void LoadLoginConfiguration()
        {
            txtStationName.Text = _currentLoginConfig.StationName;
            txtWelcomeMessage.Text = _currentLoginConfig.WelcomeMessage;
            txtSubtitle.Text = _currentLoginConfig.SubtitleMessage;

            btnPrimaryColor.BackColor = _currentLoginConfig.GetPrimaryColor();
            btnStationTextColor.BackColor = _currentLoginConfig.GetStationTextColor();

            chkUseCustomLogo.Checked = _currentLoginConfig.UseCustomLogo;
            txtLogoPath.Text = _currentLoginConfig.LogoPath;

            chkUseBackgroundImage.Checked = _currentLoginConfig.UseBackgroundImage;
            txtBackgroundImagePath.Text = _currentLoginConfig.BackgroundImagePath;

            UpdateLoginControlStates();
            UpdateLoginPreview();
        }

        private void UpdateLoginControlStates()
        {
            lblLogoPath.Enabled = chkUseCustomLogo.Checked;
            txtLogoPath.Enabled = chkUseCustomLogo.Checked;
            btnBrowseLogo.Enabled = chkUseCustomLogo.Checked;

            lblBackgroundImagePath.Enabled = chkUseBackgroundImage.Checked;
            txtBackgroundImagePath.Enabled = chkUseBackgroundImage.Checked;
            btnBrowseBackground.Enabled = chkUseBackgroundImage.Checked;
        }

        private void SelectLoginColor(Button colorButton, string colorName)
        {
            colorDialog.Color = colorButton.BackColor;
            colorDialog.FullOpen = true;

            if (colorDialog.ShowDialog() == DialogResult.OK)
            {
                colorButton.BackColor = colorDialog.Color;
                UpdateLoginPreview();
            }
        }

        private void BtnBrowseLogo_Click(object sender, EventArgs e)
        {
            openFileDialog.Title = "Select Logo Image";
            if (openFileDialog.ShowDialog() == DialogResult.OK)
            {
                txtLogoPath.Text = openFileDialog.FileName;
                UpdateLoginPreview();
            }
        }

        private void BtnBrowseBackground_Click(object sender, EventArgs e)
        {
            openFileDialog.Title = "Select Background Image";
            if (openFileDialog.ShowDialog() == DialogResult.OK)
            {
                txtBackgroundImagePath.Text = openFileDialog.FileName;
                UpdateLoginPreview();
            }
        }

        private void ChkUseCustomLogo_CheckedChanged(object sender, EventArgs e)
        {
            UpdateLoginControlStates();
            UpdateLoginPreview();
        }

        private void ChkUseBackgroundImage_CheckedChanged(object sender, EventArgs e)
        {
            UpdateLoginControlStates();
            UpdateLoginPreview();
        }

        private void UpdateLoginPreview()
        {
            // Create a simple preview display
            previewPanel.Controls.Clear();

            // Create preview content
            Label previewTitle = new Label
            {
                Text = "Login Preview",
                Font = new Font("Segoe UI", 12, FontStyle.Bold),
                Location = new Point(10, 10),
                AutoSize = true,
                ForeColor = Color.FromArgb(33, 37, 41)
            };

            Panel colorPreview = new Panel
            {
                BackColor = btnPrimaryColor.BackColor,
                Location = new Point(10, 40),
                Size = new Size(previewPanel.Width - 20, 60),
                Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right
            };

            Label stationPreview = new Label
            {
                Text = !string.IsNullOrEmpty(txtStationName.Text) ? txtStationName.Text : "Station Name",
                Font = new Font("Segoe UI", 14, FontStyle.Bold),
                ForeColor = btnStationTextColor.BackColor,
                Location = new Point(10, 10),
                AutoSize = true,
                BackColor = Color.Transparent
            };

            Label welcomePreview = new Label
            {
                Text = !string.IsNullOrEmpty(txtWelcomeMessage.Text) ? txtWelcomeMessage.Text : "Welcome Message",
                Font = new Font("Segoe UI", 10, FontStyle.Regular),
                ForeColor = btnStationTextColor.BackColor,
                Location = new Point(10, 35),
                AutoSize = true,
                BackColor = Color.Transparent
            };

            colorPreview.Controls.AddRange(new Control[] { stationPreview, welcomePreview });

            Label infoLabel = new Label
            {
                Text = "This is a simplified preview of the login configuration.",
                Font = new Font("Segoe UI", 9, FontStyle.Italic),
                Location = new Point(10, 110),
                Size = new Size(previewPanel.Width - 20, 40),
                ForeColor = Color.Gray,
                Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right
            };

            previewPanel.Controls.AddRange(new Control[] { previewTitle, colorPreview, infoLabel });
        }

        private void BtnSaveLoginConfig_Click(object sender, EventArgs e)
        {
            try
            {
                // Validate inputs
                if (string.IsNullOrWhiteSpace(txtStationName.Text))
                {
                    MessageBox.Show("Please enter a station name.", "Validation Error", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                // Update configuration
                _currentLoginConfig.StationName = txtStationName.Text.Trim();
                _currentLoginConfig.WelcomeMessage = txtWelcomeMessage.Text.Trim();
                _currentLoginConfig.SubtitleMessage = txtSubtitle.Text.Trim();

                _currentLoginConfig.SetPrimaryColor(btnPrimaryColor.BackColor);
                _currentLoginConfig.SetStationTextColor(btnStationTextColor.BackColor);

                _currentLoginConfig.UseCustomLogo = chkUseCustomLogo.Checked;
                _currentLoginConfig.UseBackgroundImage = chkUseBackgroundImage.Checked;

                // Copy images to application directory if needed
                if (_currentLoginConfig.UseCustomLogo && !string.IsNullOrEmpty(txtLogoPath.Text))
                {
                    _currentLoginConfig.LogoPath = _loginConfigService.CopyImageToAppDirectory(txtLogoPath.Text, "logo");
                }
                else
                {
                    _currentLoginConfig.LogoPath = "";
                }

                if (_currentLoginConfig.UseBackgroundImage && !string.IsNullOrEmpty(txtBackgroundImagePath.Text))
                {
                    _currentLoginConfig.BackgroundImagePath = _loginConfigService.CopyImageToAppDirectory(txtBackgroundImagePath.Text, "background");
                }
                else
                {
                    _currentLoginConfig.BackgroundImagePath = "";
                }

                // Save configuration
                _loginConfigService.SaveLoginConfiguration(_currentLoginConfig);

                MessageBox.Show("Login configuration has been saved successfully!",
                    "Configuration Saved", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error saving login configuration: {ex.Message}",
                    "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnResetLoginConfig_Click(object sender, EventArgs e)
        {
            if (MessageBox.Show("Are you sure you want to reset all login configuration settings to default values?",
                "Reset Configuration", MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
            {
                _currentLoginConfig = new LoginConfiguration();
                LoadLoginConfiguration();
            }
        }




    }
}