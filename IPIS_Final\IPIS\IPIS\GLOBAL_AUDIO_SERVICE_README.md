# Global Audio Service Implementation

## Overview

The Global Audio Service is a singleton service that manages audio playback independently of form switching. This ensures that audio announcements continue playing when users switch between different forms in the IPIS application.

## Problem Solved

Previously, when an announcement was playing and the user switched to another form (e.g., from AnnouncementBoardForm to AdvertisingForm), the audio would stop. This was because the AudioPlayer was instantiated within the AnnouncementBoardForm and was tied to the form's lifecycle.

## Solution

### GlobalAudioService Class

The `GlobalAudioService` class implements the Singleton pattern to ensure only one instance exists throughout the application lifecycle:

```csharp
public class GlobalAudioService : IDisposable
{
    private static GlobalAudioService _instance;
    private static readonly object _lock = new object();
    private AudioPlayer _audioPlayer;
    
    public static GlobalAudioService Instance
    {
        get
        {
            if (_instance == null)
            {
                lock (_lock)
                {
                    if (_instance == null)
                    {
                        _instance = new GlobalAudioService();
                    }
                }
            }
            return _instance;
        }
    }
}
```

### Key Features

1. **Singleton Pattern**: Ensures only one audio service instance exists
2. **Thread-Safe**: Uses double-checked locking for thread safety
3. **Form-Independent**: Audio playback continues regardless of active form
4. **Event-Driven**: Provides events for playback completion and status changes
5. **Proper Disposal**: Includes proper cleanup when application exits

### Methods Available

- `PlayAudio(List<string> audioFiles, int repeatCount)`: Starts audio playback
- `PauseAudio()`: Pauses current playback
- `ResumeAudio()`: Resumes paused playback
- `StopAudio()`: Stops current playback
- `SetVolume(int volume)`: Sets audio volume (0-100)
- `IsPlaying()`: Checks if audio is currently playing
- `IsPaused()`: Checks if audio is currently paused
- `GetCurrentAudioIndex()`: Gets current audio file index
- `GetTotalAudioFiles()`: Gets total number of audio files in queue

## Implementation Changes

### AnnouncementBoardForm Updates

1. **Replaced AudioPlayer with GlobalAudioService**:
   ```csharp
   // Before
   private AudioPlayer audioPlayer;
   
   // After
   private readonly GlobalAudioService globalAudioService;
   ```

2. **Updated Constructor**:
   ```csharp
   // Initialize global audio service for independent playback
   globalAudioService = GlobalAudioService.Instance;
   globalAudioService.SetStatusCallback(UpdateStatus);
   globalAudioService.PlaybackCompleted += (s, e) =>
   {
       UpdateStatus("Audio playback completed");
       ClearCurrentAnnouncementHighlight();
       LogAnnouncementCompletion();
   };
   globalAudioService.StatusChanged += AudioPlayer_StatusChanged;
   
   // Subscribe to form activation event to update UI when returning to this form
   this.Activated += AnnouncementBoardForm_Activated;
   ```

3. **Updated Method Calls**:
   ```csharp
   // Before
   audioPlayer.StartPlayback();
   audioPlayer.StopPlayback();
   audioPlayer.SetVolume(volume);
   
   // After
   globalAudioService.PlayAudio(audioQueue, repeatCount);
   globalAudioService.StopAudio();
   globalAudioService.SetVolume(volume);
   ```

4. **Added Form Activation Handler**:
   ```csharp
   private void AnnouncementBoardForm_Activated(object sender, EventArgs e)
   {
       // Update UI when form is activated (brought to front)
       // Updates button states and status based on current audio state
   }
   ```

5. **Modified Dispose Method**:
   ```csharp
   // Removed audio stopping from Dispose to preserve playback across form switches
   // The global audio service continues playing even when form is disposed
   ```

### MainForm Updates

1. **Added proper disposal of the GlobalAudioService when the application exits**:
   ```csharp
   protected override void OnFormClosing(FormClosingEventArgs e)
   {
       // ... existing code ...
       
       // Dispose of the global audio service before exiting
       try
       {
           GlobalAudioService.DisposeInstance();
       }
       catch (Exception ex)
       {
           System.Diagnostics.Debug.WriteLine($"Error disposing GlobalAudioService: {ex.Message}");
       }
       
       // ... existing code ...
   }
   ```

2. **Modified OpenForm method to preserve AnnouncementBoardForm instances**:
   ```csharp
   // Special handling for AnnouncementBoardForm to preserve audio playback
   foreach (Form existingForm in this.MdiChildren)
   {
       if (existingForm.GetType() == form.GetType())
       {
           // For AnnouncementBoardForm, just bring to front instead of closing
           if (form is AnnouncementBoardForm)
           {
               existingForm.BringToFront();
               existingForm.WindowState = FormWindowState.Maximized;
               return; // Exit early, don't create new instance
           }
           else
           {
               existingForm.Close();
           }
           break;
       }
   }
   ```

### AdvertisingForm Updates

Updated the wave file testing functionality to use GlobalAudioService:

```csharp
private async Task PlayWaveFilesSequentially(List<string> waveFiles)
{
    if (waveFiles == null || waveFiles.Count == 0)
        return;
    
    var tcs = new TaskCompletionSource<bool>();
    var globalAudioService = GlobalAudioService.Instance;
    
    // Create a proper event handler that can be unsubscribed
    EventHandler playbackCompletedHandler = (s, e) => tcs.TrySetResult(true);
    
    // Subscribe to playback completion
    globalAudioService.PlaybackCompleted += playbackCompletedHandler;
    
    // Play the audio files
    globalAudioService.PlayAudio(waveFiles, 1);
    
    // Wait for completion
    await tcs.Task;
    
    // Unsubscribe from the event
    globalAudioService.PlaybackCompleted -= playbackCompletedHandler;
}
```

## Benefits

1. **Continuous Playback**: Audio continues playing when switching forms
2. **Better User Experience**: Users can navigate the application without interrupting announcements
3. **Centralized Audio Management**: Single point of control for all audio playback
4. **Resource Efficiency**: Only one audio player instance for the entire application
5. **Consistent Behavior**: All forms use the same audio service

## Usage

### Basic Usage

```csharp
// Get the global audio service instance
var audioService = GlobalAudioService.Instance;

// Play audio files
audioService.PlayAudio(audioFileList, repeatCount);

// Control playback
audioService.PauseAudio();
audioService.ResumeAudio();
audioService.StopAudio();

// Set volume
audioService.SetVolume(75);
```

### Event Handling

```csharp
// Subscribe to events
audioService.PlaybackCompleted += (s, e) => 
{
    Console.WriteLine("Playback completed");
};

audioService.StatusChanged += (s, status) => 
{
    Console.WriteLine($"Status: {status}");
};
```

## File Structure

- `Services/GlobalAudioService.cs`: Main service implementation
- `Forms/Announcement/AnnouncementBoardForm.cs`: Updated to use GlobalAudioService
- `Forms/MainForm.cs`: Updated to dispose GlobalAudioService on exit
- `Forms/Advertising/AdvertisingForm.cs`: Updated wave file testing

## Notes

- The GlobalAudioService is automatically initialized when first accessed
- It's properly disposed when the application exits
- All existing functionality is preserved while adding form-independent playback
- The service is thread-safe and can be used from any form or component 