using System;

namespace IPIS.Utils
{
    public static class Permissions
    {
        public const string PlayAnnouncements = "Play Announcements";
        public const string PlaySlogans = "Play Slogans";
        public const string AddTrainDetails = "Add Train Details";
        public const string EditTrainDetails = "Edit Train Details";
        public const string ManageTrainTypes = "Manage Train Types";
        public const string LoadDataFromApi = "Load Data from API";
        public const string ManageStation = "Manage Station";
        public const string ManageUsers = "Manage Users";
        public const string ChangePassword = "Change Password";
        public const string Logs = "Logs";
        public const string ConfigureCurrentStation = "Configure current station";
        public const string SetCurrentStation = "Set Current Station";
        public const string ManageStationDetails = "Manage Station Details";
        public const string AddAdvertising = "Add Advertising";
        public const string Reports = "Reports";
        public const string ManageAdvertising = "Manage Advertising";
        public const string ManageAdvertising2 = "Manage Advertising"; // Duplicate for compatibility
        // Add more as needed
    }
} 