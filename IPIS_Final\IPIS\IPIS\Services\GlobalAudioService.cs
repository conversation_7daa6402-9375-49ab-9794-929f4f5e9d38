using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using IPIS.Utils;

namespace IPIS.Services
{
    /// <summary>
    /// Global audio service that manages audio playback independently of form switching
    /// This ensures audio continues playing when switching between forms
    /// </summary>
    public class GlobalAudioService : IDisposable
    {
        private static GlobalAudioService _instance;
        private static readonly object _lock = new object();
        private AudioPlayer _audioPlayer;
        private bool _isDisposed = false;

        public event EventHandler PlaybackCompleted;
        public event EventHandler<string> StatusChanged;

        private GlobalAudioService()
        {
            InitializeAudioPlayer();
        }

        public static GlobalAudioService Instance
        {
            get
            {
                if (_instance == null)
                {
                    lock (_lock)
                    {
                        if (_instance == null)
                        {
                            _instance = new GlobalAudioService();
                        }
                    }
                }
                return _instance;
            }
        }

        private void InitializeAudioPlayer()
        {
            try
            {
                _audioPlayer = new AudioPlayer();
                _audioPlayer.PlaybackCompleted += (s, e) => PlaybackCompleted?.Invoke(this, e);
                _audioPlayer.StatusChanged += (s, status) => StatusChanged?.Invoke(this, status);
                
                // Set status callback for logging
                _audioPlayer.SetStatusCallback((status) => 
                {
                    Console.WriteLine($"[GlobalAudioService] {status}");
                });
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error initializing GlobalAudioService: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Sets the audio queue and starts playback
        /// </summary>
        /// <param name="audioFiles">List of audio file paths</param>
        /// <param name="repeatCount">Number of times to repeat the queue</param>
        public void PlayAudio(List<string> audioFiles, int repeatCount = 1)
        {
            if (_audioPlayer != null && !_isDisposed)
            {
                _audioPlayer.SetAudioQueue(audioFiles, repeatCount);
                _audioPlayer.StartPlayback();
            }
        }

        /// <summary>
        /// Pauses the current audio playback
        /// </summary>
        public void PauseAudio()
        {
            if (_audioPlayer != null && !_isDisposed)
            {
                _audioPlayer.PausePlayback();
            }
        }

        /// <summary>
        /// Resumes the paused audio playback
        /// </summary>
        public void ResumeAudio()
        {
            if (_audioPlayer != null && !_isDisposed)
            {
                _audioPlayer.ResumePlayback();
            }
        }

        /// <summary>
        /// Stops the current audio playback
        /// </summary>
        public void StopAudio()
        {
            if (_audioPlayer != null && !_isDisposed)
            {
                _audioPlayer.StopPlayback();
            }
        }

        /// <summary>
        /// Sets the volume for audio playback
        /// </summary>
        /// <param name="volume">Volume level (0-100)</param>
        public void SetVolume(int volume)
        {
            if (_audioPlayer != null && !_isDisposed)
            {
                _audioPlayer.SetVolume(volume);
            }
        }

        /// <summary>
        /// Gets the current volume level
        /// </summary>
        /// <returns>Current volume level (0-100)</returns>
        public int GetVolume()
        {
            if (_audioPlayer != null && !_isDisposed)
            {
                return _audioPlayer.GetVolume();
            }
            return AudioConfig.DEFAULT_VOLUME;
        }

        /// <summary>
        /// Checks if audio is currently playing
        /// </summary>
        /// <returns>True if audio is playing, false otherwise</returns>
        public bool IsPlaying()
        {
            return _audioPlayer != null && !_isDisposed && _audioPlayer.IsPlaying;
        }

        /// <summary>
        /// Checks if audio is currently paused
        /// </summary>
        /// <returns>True if audio is paused, false otherwise</returns>
        public bool IsPaused()
        {
            return _audioPlayer != null && !_isDisposed && _audioPlayer.IsPaused;
        }

        /// <summary>
        /// Gets the current audio index in the queue
        /// </summary>
        /// <returns>Current audio index</returns>
        public int GetCurrentAudioIndex()
        {
            if (_audioPlayer != null && !_isDisposed)
            {
                return _audioPlayer.CurrentAudioIndex;
            }
            return 0;
        }

        /// <summary>
        /// Gets the total number of audio files in the queue
        /// </summary>
        /// <returns>Total number of audio files</returns>
        public int GetTotalAudioFiles()
        {
            if (_audioPlayer != null && !_isDisposed)
            {
                return _audioPlayer.TotalAudioFiles;
            }
            return 0;
        }

        /// <summary>
        /// Sets a custom status callback for detailed logging
        /// </summary>
        /// <param name="callback">Callback function to handle status updates</param>
        public void SetStatusCallback(Action<string> callback)
        {
            if (_audioPlayer != null && !_isDisposed)
            {
                _audioPlayer.SetStatusCallback(callback);
            }
        }

        /// <summary>
        /// Gets the current status of the audio service
        /// </summary>
        /// <returns>Current status string</returns>
        public string GetCurrentStatus()
        {
            if (_audioPlayer == null || _isDisposed)
                return "Audio service not available";
            
            if (_audioPlayer.IsPlaying)
                return "Playing";
            else if (_audioPlayer.IsPaused)
                return "Paused";
            else
                return "Stopped";
        }

        public void Dispose()
        {
            if (!_isDisposed)
            {
                try
                {
                    if (_audioPlayer != null)
                    {
                        _audioPlayer.StopPlayback();
                        _audioPlayer.Dispose();
                        _audioPlayer = null;
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error disposing GlobalAudioService: {ex.Message}");
                }
                finally
                {
                    _isDisposed = true;
                }
            }
        }

        /// <summary>
        /// Static method to dispose the singleton instance
        /// </summary>
        public static void DisposeInstance()
        {
            if (_instance != null)
            {
                lock (_lock)
                {
                    if (_instance != null)
                    {
                        _instance.Dispose();
                        _instance = null;
                    }
                }
            }
        }
    }
} 