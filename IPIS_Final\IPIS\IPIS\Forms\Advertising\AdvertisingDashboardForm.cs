using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using System.Data;
using IPIS.Services;
using IPIS.Repositories;
using IPIS.Utils;
using System.Threading.Tasks;

namespace IPIS.Forms.Advertising
{
    public partial class AdvertisingDashboardForm : Form
    {
        private readonly AdvertisingService advertisingService;
        private readonly TrainService trainService;
        private readonly StationService stationService;
        private readonly ToastNotification toast;
        
        // UI Controls
        private Panel mainPanel;
        private Panel timeColumnPanel;
        private Panel schedulePanel;
        private Panel headerPanel;
        private VScrollBar vScrollBar;
        private HScrollBar hScrollBar;
        private Button btnRefresh;
        private Button btnToday;
        private Button btnPreviousDay;
        private Button btnNextDay;
        private Label lblDate;
        private NumericUpDown numGapMinutes;
        private Label lblGapLabel;
        
        // Data
        private DateTime currentDate;
        private DateTime displayStartTime;
        private DateTime displayEndTime;
        private int gapMinutes = 5; // Default gap between train announcements
        private List<ScheduleItem> scheduleItems;
        private List<string> platforms;
        
        // Constants
        private const int HOUR_HEIGHT = 80; // Increased from 60 to 80
        private const int TIME_COLUMN_WIDTH = 80;
        private const int PLATFORM_COLUMN_WIDTH = 120;
        private const int MIN_ITEM_HEIGHT = 25; // Increased from 20 to 25
        private const int ITEM_PADDING = 2;
        
        // Add a new panel to wrap all scrollable schedule content
        private Panel contentPanel;
        
        public AdvertisingDashboardForm()
        {
            advertisingService = new AdvertisingService(new SQLiteAdvertisingRepository());
            trainService = new TrainService(new SQLiteTrainRepository());
            stationService = new StationService(new SQLiteStationRepository());
            toast = new ToastNotification(this);
            scheduleItems = new List<ScheduleItem>();
            
            InitializeComponent();
            LoadData();
        }
        
        private void InitializeComponent()
        {
            this.Text = "Advertising Dashboard";
            this.Size = new Size(1400, 900);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.WindowState = FormWindowState.Maximized;
            
            // Set current date
            currentDate = DateTime.Today;
            displayStartTime = DateTime.Now; // Start from current time
            displayEndTime = displayStartTime.AddHours(25);
            
            // Main layout
            var mainLayout = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 1,
                RowCount = 2,
                Padding = new Padding(10)
            };
            mainLayout.RowStyles.Add(new RowStyle(SizeType.AutoSize)); // Header
            mainLayout.RowStyles.Add(new RowStyle(SizeType.Percent, 100)); // Schedule area
            this.Controls.Add(mainLayout);
            
            // Header panel
            CreateHeaderPanel(mainLayout);
            
            // Schedule area
            CreateScheduleArea(mainLayout);
        }
        
        private void CreateHeaderPanel(TableLayoutPanel parent)
        {
            headerPanel = new Panel
            {
                Dock = DockStyle.Fill,
                Height = 60,
                BackColor = UIStyler.Colors.Primary
            };

            // Only show the button bar to Operator and Supervisor
            if (IPIS.Utils.SessionManager.IsOperator() || IPIS.Utils.SessionManager.IsSupervisor())
            {
                var headerLayout = new FlowLayoutPanel
                {
                    Dock = DockStyle.Fill,
                    FlowDirection = FlowDirection.LeftToRight,
                    Padding = new Padding(10, 15, 10, 15)
                };

                // Navigation buttons
                btnPreviousDay = new Button { Text = "◀", Width = 40, Height = 30 };
                ButtonStyler.ApplyStandardStyle(btnPreviousDay, "secondary", "small");
                btnPreviousDay.Click += (s, e) => NavigateDay(-1);

                lblDate = new Label
                {
                    Text = currentDate.ToString("dddd, MMMM dd, yyyy"),
                    Width = 250,
                    Height = 30,
                    TextAlign = ContentAlignment.MiddleCenter,
                    Font = new Font(Font, FontStyle.Bold),
                    ForeColor = Color.White
                };

                btnNextDay = new Button { Text = "▶", Width = 40, Height = 30 };
                ButtonStyler.ApplyStandardStyle(btnNextDay, "secondary", "small");
                btnNextDay.Click += (s, e) => NavigateDay(1);

                btnToday = new Button { Text = "Today", Width = 80, Height = 30 };
                ButtonStyler.ApplyStandardStyle(btnToday, "info", "small");
                btnToday.Click += (s, e) => GoToToday();

                // Gap configuration
                lblGapLabel = new Label
                {
                    Text = "Announcement Gap (min):",
                    Width = 150,
                    Height = 30,
                    TextAlign = ContentAlignment.MiddleRight,
                    ForeColor = Color.White
                };

                numGapMinutes = new NumericUpDown
                {
                    Minimum = 1,
                    Maximum = 30,
                    Value = gapMinutes,
                    Width = 60,
                    Height = 30
                };
                UIStyler.ApplyNumericUpDownStyle(numGapMinutes, "small");
                numGapMinutes.ValueChanged += (s, e) => 
                {
                    gapMinutes = (int)numGapMinutes.Value;
                    RefreshSchedule();
                };

                // Refresh button
                btnRefresh = new Button { Text = "Refresh", Width = 80, Height = 30 };
                ButtonStyler.ApplyStandardStyle(btnRefresh, "primary", "small");
                btnRefresh.Click += (s, e) => LoadData();

                headerLayout.Controls.AddRange(new Control[]
                {
                    btnPreviousDay, lblDate, btnNextDay, btnToday,
                    new Label { Width = 20 }, // Spacer
                    lblGapLabel, numGapMinutes,
                    new Label { Width = 20 }, // Spacer
                    btnRefresh
                });

                headerPanel.Controls.Add(headerLayout);
            }
            parent.Controls.Add(headerPanel, 0, 0);
        }
        
        private void CreateScheduleArea(TableLayoutPanel parent)
        {
            var scheduleContainer = new Panel
            {
                Dock = DockStyle.Fill,
                BackColor = Color.White
            };
            
            // Time column (left side)
            timeColumnPanel = new Panel
            {
                Width = TIME_COLUMN_WIDTH,
                BackColor = Color.LightGray,
                BorderStyle = BorderStyle.FixedSingle
            };
            
            // Main schedule panel
            schedulePanel = new Panel
            {
                Dock = DockStyle.Fill,
                BackColor = Color.White,
                AutoScroll = false
            };
            
            // Scroll bars
            vScrollBar = new VScrollBar
            {
                Dock = DockStyle.Right,
                Width = 20
            };
            vScrollBar.ValueChanged += (s, e) => UpdateScrollPosition();
            
            hScrollBar = new HScrollBar
            {
                Dock = DockStyle.Bottom,
                Height = 20
            };
            hScrollBar.ValueChanged += (s, e) => UpdateScrollPosition();
            
            schedulePanel.Controls.Add(vScrollBar);
            schedulePanel.Controls.Add(hScrollBar);
            
            scheduleContainer.Controls.Add(schedulePanel);
            parent.Controls.Add(scheduleContainer, 0, 1);
        }
        
        private async void LoadData()
        {
            try
            {
                // Load platforms
                LoadPlatforms();
                
                // Load train schedules
                await LoadTrainSchedules();
                
                // Load advertisements
                LoadAdvertisements();
                
                // Generate schedule items
                GenerateScheduleItems();
                
                // Render the schedule
                RenderSchedule();
                
                toast.ShowSuccess("Dashboard data loaded successfully");
            }
            catch (Exception ex)
            {
                toast.ShowError($"Error loading dashboard data: {ex.Message}");
            }
        }
        
        private void LoadPlatforms()
        {
            platforms = new List<string>();
            var currentStation = stationService.GetCurrentStation();
            
            if (currentStation != null)
            {
                for (int i = 1; i <= currentStation.AvailablePF && i <= 10; i++)
                {
                    string platformValue = "";
                    switch (i)
                    {
                        case 1: platformValue = currentStation.P1; break;
                        case 2: platformValue = currentStation.P2; break;
                        case 3: platformValue = currentStation.P3; break;
                        case 4: platformValue = currentStation.P4; break;
                        case 5: platformValue = currentStation.P5; break;
                        case 6: platformValue = currentStation.P6; break;
                        case 7: platformValue = currentStation.P7; break;
                        case 8: platformValue = currentStation.P8; break;
                        case 9: platformValue = currentStation.P9; break;
                        case 10: platformValue = currentStation.P10; break;
                    }
                    
                    if (!string.IsNullOrEmpty(platformValue))
                    {
                        platforms.Add(platformValue);
                    }
                    else
                    {
                        platforms.Add(i.ToString());
                    }
                }
            }
            else
            {
                // Fallback to default platforms
                for (int i = 1; i <= 10; i++)
                {
                    platforms.Add(i.ToString());
                }
            }
        }
        
        private async Task LoadTrainSchedules()
        {
            // Get trains for the current day from Train_Data table
            var trains = trainService.GetAllTrains();
            scheduleItems.Clear();
            
            // Limit to 25 trains to avoid duplicate data
            int trainCount = 0;
            const int MAX_TRAINS = 25;
            
            // Calculate the time range for next 24 hours
            DateTime now = DateTime.Now;
            DateTime endTime = now.AddHours(24);
            
            foreach (DataRow train in trains.Rows)
            {
                if (trainCount >= MAX_TRAINS) break;
                
                string trainNo = train["Train_No"].ToString();
                string trainName = train["Train_NameEng"].ToString();
                string platform = train["Sch_PF"].ToString();
                string trainAD = train["Train_AD"].ToString();
                
                // Parse arrival and departure times
                if (TimeSpan.TryParse(train["Sch_AT"].ToString(), out TimeSpan arrTime) &&
                    TimeSpan.TryParse(train["Sch_DT"].ToString(), out TimeSpan depTime))
                {
                    // Check for trains in the next 24 hours
                    // First check today
                    if (IsTrainOperatingOnDay(train, DateTime.Today))
                    {
                        DateTime arrDateTime = DateTime.Today.Add(arrTime);
                        DateTime depDateTime = DateTime.Today.Add(depTime);
                        
                        // Add arrival announcement if it's in the future and within 24 hours
                        if ((trainAD == "A" || trainAD == "Both") && arrDateTime > now && arrDateTime <= endTime)
                        {
                            scheduleItems.Add(new ScheduleItem
                            {
                                Type = ScheduleItemType.TrainArrival,
                                Title = $"Train {trainNo} - {trainName}",
                                Subtitle = "Arrival",
                                StartTime = arrDateTime.AddMinutes(-gapMinutes),
                                EndTime = arrDateTime.AddMinutes(gapMinutes),
                                Platform = platform,
                                Color = Color.LightBlue,
                                BorderColor = Color.Blue
                            });
                            trainCount++;
                        }
                        
                        // Add departure announcement if it's in the future and within 24 hours
                        if ((trainAD == "D" || trainAD == "Both") && depDateTime > now && depDateTime <= endTime)
                        {
                            scheduleItems.Add(new ScheduleItem
                            {
                                Type = ScheduleItemType.TrainDeparture,
                                Title = $"Train {trainNo} - {trainName}",
                                Subtitle = "Departure",
                                StartTime = depDateTime.AddMinutes(-gapMinutes),
                                EndTime = depDateTime.AddMinutes(gapMinutes),
                                Platform = platform,
                                Color = Color.LightGreen,
                                BorderColor = Color.Green
                            });
                            trainCount++;
                        }
                    }
                    
                    // Then check tomorrow
                    if (trainCount < MAX_TRAINS && IsTrainOperatingOnDay(train, DateTime.Today.AddDays(1)))
                    {
                        DateTime arrDateTime = DateTime.Today.AddDays(1).Add(arrTime);
                        DateTime depDateTime = DateTime.Today.AddDays(1).Add(depTime);
                        
                        // Add arrival announcement if it's within 24 hours
                        if ((trainAD == "A" || trainAD == "Both") && arrDateTime <= endTime)
                        {
                            scheduleItems.Add(new ScheduleItem
                            {
                                Type = ScheduleItemType.TrainArrival,
                                Title = $"Train {trainNo} - {trainName}",
                                Subtitle = "Arrival",
                                StartTime = arrDateTime.AddMinutes(-gapMinutes),
                                EndTime = arrDateTime.AddMinutes(gapMinutes),
                                Platform = platform,
                                Color = Color.LightBlue,
                                BorderColor = Color.Blue
                            });
                            trainCount++;
                        }
                        
                        // Add departure announcement if it's within 24 hours
                        if ((trainAD == "D" || trainAD == "Both") && depDateTime <= endTime)
                        {
                            scheduleItems.Add(new ScheduleItem
                            {
                                Type = ScheduleItemType.TrainDeparture,
                                Title = $"Train {trainNo} - {trainName}",
                                Subtitle = "Departure",
                                StartTime = depDateTime.AddMinutes(-gapMinutes),
                                EndTime = depDateTime.AddMinutes(gapMinutes),
                                Platform = platform,
                                Color = Color.LightGreen,
                                BorderColor = Color.Green
                            });
                            trainCount++;
                        }
                    }
                }
            }
        }
        
        private void LoadAdvertisements()
        {
            var advertisements = advertisingService.GetAllAdvertisements();
            
            foreach (DataRow ad in advertisements.Rows)
            {
                string adverName = ad["Adver_Name"].ToString();
                string annType = ad["Ann_Type"].ToString();
                string timeSlot = ad["TimeSlot"].ToString();
                string trainNumbers = ad["TrainNumber"].ToString();
                string playPosition = ad["PlayPosition"].ToString();
                
                // Handle time slot based advertisements
                if (!string.IsNullOrEmpty(timeSlot))
                {
                    var timeSlots = ParseTimeSlots(timeSlot);
                    foreach (var (startHour, startMinute, endHour, endMinute) in timeSlots)
                    {
                        DateTime startTime = currentDate.AddHours(startHour).AddMinutes(startMinute);
                        DateTime endTime = currentDate.AddHours(endHour).AddMinutes(endMinute);
                        
                        scheduleItems.Add(new ScheduleItem
                        {
                            Type = ScheduleItemType.Advertisement,
                            Title = adverName,
                            Subtitle = annType,
                            StartTime = startTime,
                            EndTime = endTime,
                            Platform = "ALL",
                            Color = Color.LightPink,
                            BorderColor = Color.Purple
                        });
                    }
                }
                
                // Handle train-based advertisements
                if (!string.IsNullOrEmpty(trainNumbers))
                {
                    var trainList = trainNumbers.Split(',');
                    foreach (string trainNo in trainList)
                    {
                        var trainNoTrim = trainNo.Trim();
                        if (!string.IsNullOrEmpty(trainNoTrim))
                        {
                            // Find the train schedule and add advertisement
                            var trainItems = scheduleItems.Where(si => 
                                si.Title.Contains(trainNoTrim) && 
                                (si.Type == ScheduleItemType.TrainArrival || si.Type == ScheduleItemType.TrainDeparture)).ToList();
                            
                            foreach (var trainItem in trainItems)
                            {
                                DateTime adStartTime, adEndTime;
                                
                                if (playPosition == "Before")
                                {
                                    adStartTime = trainItem.StartTime.AddMinutes(-2);
                                    adEndTime = trainItem.StartTime;
                                }
                                else if (playPosition == "After")
                                {
                                    adStartTime = trainItem.EndTime;
                                    adEndTime = trainItem.EndTime.AddMinutes(2);
                                }
                                else // Both
                                {
                                    adStartTime = trainItem.StartTime.AddMinutes(-1);
                                    adEndTime = trainItem.EndTime.AddMinutes(1);
                                }
                                
                                scheduleItems.Add(new ScheduleItem
                                {
                                    Type = ScheduleItemType.Advertisement,
                                    Title = adverName,
                                    Subtitle = $"{annType} ({playPosition})",
                                    StartTime = adStartTime,
                                    EndTime = adEndTime,
                                    Platform = trainItem.Platform,
                                    Color = Color.LightYellow,
                                    BorderColor = Color.Orange
                                });
                            }
                        }
                    }
                }
            }
        }
        
        private List<(int startHour, int startMinute, int endHour, int endMinute)> ParseTimeSlots(string timeSlotString)
        {
            var slots = new List<(int startHour, int startMinute, int endHour, int endMinute)>();
            
            if (string.IsNullOrEmpty(timeSlotString))
                return slots;
                
            var slotStrings = timeSlotString.Split(';');
            foreach (var slotStr in slotStrings)
            {
                if (string.IsNullOrWhiteSpace(slotStr)) continue;
                
                var parts = slotStr.Split('-');
                if (parts.Length == 2)
                {
                    var startParts = parts[0].Split(':');
                    var endParts = parts[1].Split(':');
                    
                    if (startParts.Length == 2 && endParts.Length == 2)
                    {
                        if (int.TryParse(startParts[0], out int startHour) && 
                            int.TryParse(startParts[1], out int startMinute) &&
                            int.TryParse(endParts[0], out int endHour) && 
                            int.TryParse(endParts[1], out int endMinute))
                        {
                            slots.Add((startHour, startMinute, endHour, endMinute));
                        }
                    }
                }
            }
            
            return slots;
        }
        
        private bool IsTrainOperatingOnDay(DataRow train, DateTime date)
        {
            // Check if train operates on the given day
            DayOfWeek dayOfWeek = date.DayOfWeek;
            
            switch (dayOfWeek)
            {
                case DayOfWeek.Sunday:
                    return Convert.ToBoolean(train["Chk_Sun"]);
                case DayOfWeek.Monday:
                    return Convert.ToBoolean(train["Chk_Mon"]);
                case DayOfWeek.Tuesday:
                    return Convert.ToBoolean(train["Chk_Tue"]);
                case DayOfWeek.Wednesday:
                    return Convert.ToBoolean(train["Chk_Wed"]);
                case DayOfWeek.Thursday:
                    return Convert.ToBoolean(train["Chk_Thu"]);
                case DayOfWeek.Friday:
                    return Convert.ToBoolean(train["Chk_Fri"]);
                case DayOfWeek.Saturday:
                    return Convert.ToBoolean(train["Chk_Sat"]);
                default:
                    return false;
            }
        }
        
        private void GenerateScheduleItems()
        {
            // Filter items within display time range and limit to prevent duplicates
            scheduleItems = scheduleItems.Where(item => 
                item.StartTime >= displayStartTime && 
                item.StartTime <= displayEndTime &&
                item.EndTime > displayStartTime).ToList();
            
            // Sort by start time
            scheduleItems = scheduleItems.OrderBy(item => item.StartTime).ToList();
            
            // Limit total items to prevent overwhelming the display
            if (scheduleItems.Count > 50)
            {
                scheduleItems = scheduleItems.Take(50).ToList();
            }
        }
        
        private void RenderSchedule()
        {
            // Clear existing controls
            timeColumnPanel.Controls.Clear();
            schedulePanel.Controls.Clear();

            // Add time column header directly to timeColumnPanel for always-visible heading
            var timeHeaderLabel = new Label
            {
                Text = "Time",
                Location = new Point(0, 0),
                Size = new Size(TIME_COLUMN_WIDTH, 30),
                Font = new Font(Font, FontStyle.Bold),
                TextAlign = ContentAlignment.MiddleCenter,
                BackColor = Color.LightSteelBlue,
                ForeColor = Color.Black,
                BorderStyle = BorderStyle.FixedSingle
            };
            timeColumnPanel.Controls.Add(timeHeaderLabel);

            // Calculate total height needed
            int totalHours = 25; // 25-hour period
            int totalHeight = totalHours * HOUR_HEIGHT;

            // Set up scroll bars
            vScrollBar.Maximum = Math.Max(0, totalHeight - schedulePanel.Height + 30); // +30 for header
            vScrollBar.LargeChange = schedulePanel.Height;
            vScrollBar.SmallChange = HOUR_HEIGHT;
            hScrollBar.Maximum = Math.Max(0, (platforms.Count + 1) * PLATFORM_COLUMN_WIDTH - schedulePanel.Width);
            hScrollBar.LargeChange = schedulePanel.Width;
            hScrollBar.SmallChange = PLATFORM_COLUMN_WIDTH;

            // Add scroll bars back
            schedulePanel.Controls.Add(vScrollBar);
            schedulePanel.Controls.Add(hScrollBar);

            // Create a new contentPanel to wrap all scrollable content
            contentPanel = new Panel
            {
                Location = new Point(0, 30), // below header
                Size = new Size(schedulePanel.Width, totalHeight),
                BackColor = Color.Transparent,
                AutoScroll = false
            };
            schedulePanel.Controls.Add(contentPanel);

            // Create time column (labels start at y=30 to account for header)
            CreateTimeColumn(totalHeight);
            contentPanel.Controls.Add(timeColumnPanel);

            // Create platform headers (add only to schedulePanel, not contentPanel)
            CreatePlatformHeaders();

            // Create schedule grid (add to contentPanel)
            CreateScheduleGrid(totalHeight);

            // Render schedule items (add to contentPanel)
            RenderScheduleItems();
        }
        
        private void CreateTimeColumn(int totalHeight)
        {
            timeColumnPanel.Height = totalHeight + 30; // +30 for header
            timeColumnPanel.Location = new Point(0, 0); // Header at y=0

            for (int hour = 0; hour < 25; hour++)
            {
                var timeLabel = new Label
                {
                    Text = displayStartTime.AddHours(hour).ToString("HH:00"),
                    Location = new Point(5, 30 + hour * HOUR_HEIGHT + 5), // y=30 for header offset
                    Size = new Size(70, 20),
                    Font = new Font(Font, FontStyle.Bold),
                    BackColor = Color.Transparent
                };
                timeColumnPanel.Controls.Add(timeLabel);

                // Add hour separator line
                if (hour < 24)
                {
                    var separator = new Panel
                    {
                        Location = new Point(0, 30 + (hour + 1) * HOUR_HEIGHT - 1),
                        Size = new Size(TIME_COLUMN_WIDTH, 2),
                        BackColor = Color.DarkGray
                    };
                    timeColumnPanel.Controls.Add(separator);
                }
            }
        }
        
        private void CreatePlatformHeaders()
        {
            // Create platform headers
            var platformHeaderPanel = new Panel
            {
                Location = new Point(TIME_COLUMN_WIDTH, 0),
                Size = new Size(platforms.Count * PLATFORM_COLUMN_WIDTH, 30),
                BackColor = Color.LightSteelBlue,
                BorderStyle = BorderStyle.FixedSingle
            };
            
            for (int i = 0; i < platforms.Count; i++)
            {
                var platformLabel = new Label
                {
                    Text = $"Platform {platforms[i]}",
                    Location = new Point(i * PLATFORM_COLUMN_WIDTH + 5, 5),
                    Size = new Size(PLATFORM_COLUMN_WIDTH - 10, 20),
                    Font = new Font(Font, FontStyle.Bold),
                    TextAlign = ContentAlignment.MiddleCenter,
                    BackColor = Color.Transparent,
                    ForeColor = Color.Black
                };
                
                platformHeaderPanel.Controls.Add(platformLabel);
            }
            
            schedulePanel.Controls.Add(platformHeaderPanel);
        }
        
        private void CreateScheduleGrid(int totalHeight)
        {
            // Create grid lines for platforms
            for (int i = 0; i <= platforms.Count; i++)
            {
                var gridLine = new Panel
                {
                    Location = new Point(TIME_COLUMN_WIDTH + i * PLATFORM_COLUMN_WIDTH, 0),
                    Size = new Size(1, totalHeight),
                    BackColor = Color.LightGray
                };
                contentPanel.Controls.Add(gridLine);
            }

            // Create hour separator lines
            for (int hour = 1; hour < 25; hour++)
            {
                var hourLine = new Panel
                {
                    Location = new Point(TIME_COLUMN_WIDTH, hour * HOUR_HEIGHT - 1),
                    Size = new Size(platforms.Count * PLATFORM_COLUMN_WIDTH, 1),
                    BackColor = Color.LightGray
                };
                contentPanel.Controls.Add(hourLine);
            }

            // Create vertical line for time column
            var timeColumnLine = new Panel
            {
                Location = new Point(TIME_COLUMN_WIDTH, 0),
                Size = new Size(1, totalHeight),
                BackColor = Color.DarkGray
            };
            contentPanel.Controls.Add(timeColumnLine);
        }
        
        private void RenderScheduleItems()
        {
            // Group items by (platform, hour)
            var grouped = scheduleItems.GroupBy(item => new {
                Platform = item.Platform,
                Hour = item.StartTime.Hour
            });

            foreach (var group in grouped)
            {
                var items = group.ToList();
                // Find platform column
                int platformIndex = platforms.IndexOf(items[0].Platform);
                if (platformIndex == -1) platformIndex = 0;

                // Calculate Y position for the group (first item)
                int hourIndex = items[0].StartTime.Hour;
                int y = hourIndex * HOUR_HEIGHT;
                int cellHeight = HOUR_HEIGHT;
                int itemHeight = Math.Max(MIN_ITEM_HEIGHT, cellHeight / Math.Max(1, items.Count));

                int x = TIME_COLUMN_WIDTH + platformIndex * PLATFORM_COLUMN_WIDTH + ITEM_PADDING;
                int width = PLATFORM_COLUMN_WIDTH - (ITEM_PADDING * 2);

                for (int i = 0; i < items.Count; i++)
                {
                    var item = items[i];
                    int itemY = y + i * itemHeight;
                    int height = itemHeight - 2; // small gap between stacked items

                    var itemPanel = new Panel
                    {
                        Location = new Point(x, itemY),
                        Size = new Size(width, height),
                        BackColor = item.Color,
                        BorderStyle = BorderStyle.FixedSingle,
                        Tag = item
                    };

                    var titleLabel = new Label
                    {
                        Text = item.Title,
                        Location = new Point(2, 2),
                        Size = new Size(width - 4, 16),
                        Font = new Font(Font, FontStyle.Bold),
                        BackColor = Color.Transparent,
                        ForeColor = Color.Black
                    };
                    var subtitleLabel = new Label
                    {
                        Text = item.Subtitle,
                        Location = new Point(2, 18),
                        Size = new Size(width - 4, 14),
                        Font = new Font(Font, FontStyle.Regular),
                        BackColor = Color.Transparent,
                        ForeColor = Color.DarkGray
                    };
                    var timeLabel = new Label
                    {
                        Text = $"{item.StartTime:HH:mm} - {item.EndTime:HH:mm}",
                        Location = new Point(2, height - 16),
                        Size = new Size(width - 4, 14),
                        Font = new Font(Font, FontStyle.Regular),
                        BackColor = Color.Transparent,
                        ForeColor = Color.DarkBlue,
                        TextAlign = ContentAlignment.MiddleRight
                    };
                    itemPanel.Controls.Add(titleLabel);
                    itemPanel.Controls.Add(subtitleLabel);
                    itemPanel.Controls.Add(timeLabel);
                    var toolTip = new ToolTip();
                    toolTip.SetToolTip(itemPanel, $"{item.Title}\n{item.Subtitle}\n{item.StartTime:HH:mm} - {item.EndTime:HH:mm}\nPlatform: {item.Platform}");
                    contentPanel.Controls.Add(itemPanel);
                }
            }
        }
        
        private void UpdateScrollPosition()
        {
            // Move the contentPanel vertically for both time and platform columns
            if (contentPanel != null)
            {
                contentPanel.Top = 30 - vScrollBar.Value; // 30 for header offset
            }
            // Move timeColumnPanel as well (if needed)
            // timeColumnPanel.Top = 30 - vScrollBar.Value; // now handled by contentPanel

            // Update horizontal scroll for platform columns (move contentPanel left/right)
            if (contentPanel != null)
            {
                contentPanel.Left = -hScrollBar.Value;
            }

            // Force a refresh of the schedule panel
            schedulePanel.Invalidate();
        }
        
        private void NavigateDay(int days)
        {
            // Don't allow going to previous dates
            if (days < 0 && currentDate <= DateTime.Today)
            {
                return;
            }
            
            currentDate = currentDate.AddDays(days);
            displayStartTime = currentDate.AddHours(DateTime.Now.Hour);
            displayEndTime = displayStartTime.AddHours(25);
            
            lblDate.Text = currentDate.ToString("dddd, MMMM dd, yyyy");
            LoadData();
        }
        
        private void GoToToday()
        {
            currentDate = DateTime.Today;
            displayStartTime = DateTime.Now; // Start from current time
            displayEndTime = displayStartTime.AddHours(25);
            
            lblDate.Text = currentDate.ToString("dddd, MMMM dd, yyyy");
            LoadData();
        }
        
        private void RefreshSchedule()
        {
            LoadData();
        }
    }
    
    public class ScheduleItem
    {
        public ScheduleItemType Type { get; set; }
        public string Title { get; set; }
        public string Subtitle { get; set; }
        public DateTime StartTime { get; set; }
        public DateTime EndTime { get; set; }
        public string Platform { get; set; }
        public Color Color { get; set; }
        public Color BorderColor { get; set; }
    }
    
    public enum ScheduleItemType
    {
        TrainArrival,
        TrainDeparture,
        Advertisement
    }
} 