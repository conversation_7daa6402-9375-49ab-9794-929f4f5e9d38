# Audio Configuration System

## Overview

The Audio Configuration System provides centralized control over audio playback behavior through configurable constants. This allows easy modification of audio settings without changing code in multiple places.

## Configuration File

**Location:** `Utils/AudioConfig.cs`

## Available Configuration Options

### 1. Delay Between Audio Files
```csharp
public const int DELAY_BETWEEN_AUDIO_FILES_MS = 300;
```
- **Purpose:** Controls the delay between consecutive audio files in the queue
- **Default:** 300 milliseconds (0.3 seconds)
- **Range:** Any positive integer
- **Usage:** Applied between every audio file during playback

### 2. Volume Settings
```csharp
public const int DEFAULT_VOLUME = 50;
public const int MAX_VOLUME = 100;
public const int MIN_VOLUME = 0;
```
- **Purpose:** Controls volume levels and limits
- **Default Volume:** 50% (half volume)
- **Max Volume:** 100% (full volume)
- **Min Volume:** 0% (muted)

### 3. Repeat Settings
```csharp
public const int DEFAULT_REPEAT_COUNT = 1;
public const int MAX_REPEAT_COUNT = 10;
public const int MIN_REPEAT_COUNT = 1;
```
- **Purpose:** Controls how many times the audio queue repeats
- **Default:** 1 (no repeat)
- **Max:** 10 repeats
- **Min:** 1 (at least one play)

### 4. Timer Intervals
```csharp
public const int PLAYBACK_CHECK_INTERVAL_MS = 25;
public const int AUDIO_TIMER_INTERVAL_MS = 100;
```
- **Playback Check Interval:** How often to check if current audio has finished (25ms)
- **Audio Timer Interval:** Initial playback start timer (100ms)

### 5. Audio Completion Buffer
```csharp
public const int AUDIO_COMPLETION_BUFFER_MS = 100;
```
- **Purpose:** Extra time added to audio duration to ensure it has actually finished
- **Default:** 100 milliseconds
- **Usage:** Prevents premature transition to next audio file

### 6. Default Audio Duration
```csharp
public const double DEFAULT_AUDIO_DURATION_SECONDS = 3.0;
```
- **Purpose:** Fallback duration when actual audio duration cannot be determined
- **Default:** 3.0 seconds
- **Usage:** Used when WaveFileDuration.GetDuration() fails

### 7. Debug Logging
```csharp
public const bool ENABLE_AUDIO_DEBUG_LOGGING = true;
```
- **Purpose:** Enables/disables debug logging for audio operations
- **Default:** true (enabled)
- **Usage:** Controls console output for audio debugging

## How to Modify Configuration

### 1. Change Delay Between Audio Files
To increase the delay between audio files:
```csharp
// In Utils/AudioConfig.cs
public const int DELAY_BETWEEN_AUDIO_FILES_MS = 500; // 0.5 seconds
```

To decrease the delay:
```csharp
public const int DELAY_BETWEEN_AUDIO_FILES_MS = 100; // 0.1 seconds
```

To remove delay completely:
```csharp
public const int DELAY_BETWEEN_AUDIO_FILES_MS = 0; // No delay
```

### 2. Change Default Volume
```csharp
// In Utils/AudioConfig.cs
public const int DEFAULT_VOLUME = 75; // 75% volume
```

### 3. Change Repeat Count
```csharp
// In Utils/AudioConfig.cs
public const int DEFAULT_REPEAT_COUNT = 2; // Repeat once
```

### 4. Disable Debug Logging
```csharp
// In Utils/AudioConfig.cs
public const bool ENABLE_AUDIO_DEBUG_LOGGING = false;
```

## Implementation Details

### Files Modified
1. **`Utils/AudioConfig.cs`**: New configuration file
2. **`Utils/AudioPlayer.cs`**: Updated to use configuration constants
3. **`Services/GlobalAudioService.cs`**: Updated to use default volume

### Benefits
1. **Centralized Configuration**: All audio settings in one place
2. **Easy Modification**: Change behavior without code changes
3. **Consistent Behavior**: Same settings across all audio operations
4. **Maintainable**: Clear documentation and organized constants
5. **Flexible**: Easy to adjust for different requirements

## Usage Examples

### Current Default Behavior
- Delay between files: 300ms
- Default volume: 50%
- No repeats by default
- Debug logging enabled

### Recommended Settings for Different Scenarios

#### Fast Announcements (Minimal Delay)
```csharp
public const int DELAY_BETWEEN_AUDIO_FILES_MS = 100; // 0.1 seconds
```

#### Slow, Clear Announcements (Longer Delay)
```csharp
public const int DELAY_BETWEEN_AUDIO_FILES_MS = 500; // 0.5 seconds
```

#### High Volume Environment
```csharp
public const int DEFAULT_VOLUME = 80; // 80% volume
```

#### Quiet Environment
```csharp
public const int DEFAULT_VOLUME = 30; // 30% volume
```

#### Production Environment (No Debug Logs)
```csharp
public const bool ENABLE_AUDIO_DEBUG_LOGGING = false;
```

## Notes

- Changes to configuration constants require recompilation
- All audio operations (train announcements, slogans, etc.) use these settings
- The configuration affects both the AudioPlayer and GlobalAudioService
- Settings are applied globally across the entire application 