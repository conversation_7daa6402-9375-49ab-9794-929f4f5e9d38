-- SQLite query to update <PERSON>h_<PERSON> with Sch_DT for departure trains
-- This query goes through all entries in Train_Data table
-- and copies Sch_DT value to Sch_AT when Train_AD is 'D' (departure)

UPDATE Train_Data 
SET Sch_AT = Sch_DT 
WHERE Train_AD = 'D';

-- Optional: Show the count of affected rows
-- SELECT COUNT(*) as UpdatedRows FROM Train_Data WHERE Train_AD = 'D';

-- Optional: Verify the changes by showing some examples
-- SELECT Train_No, Train_NameEng, Train_AD, Sch_AT, Sch_DT 
-- FROM Train_Data 
-- WHERE Train_AD = 'D' 
-- LIMIT 10; 